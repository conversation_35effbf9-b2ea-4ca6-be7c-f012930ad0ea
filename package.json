{"name": "big-screen-project", "version": "1.0.0", "description": "专业级大屏可视化项目 - Vue3 + TypeScript + ECharts", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview --host 0.0.0.0 --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit", "prepare": "husky install"}, "dependencies": {"vue": "^3.4.21", "echarts": "^5.5.0", "axios": "^1.6.8", "lodash-es": "^4.17.21"}, "devDependencies": {"@types/node": "^20.11.30", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "sass": "^1.72.0", "typescript": "~5.4.0", "vite": "^5.2.0", "vue-tsc": "^2.0.6", "@types/lodash-es": "^4.17.12"}, "lint-staged": {"*.{vue,js,ts}": ["eslint --fix", "prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["vue3", "typescript", "echarts", "big-screen", "visualization", "dashboard"], "author": "Big Screen Team", "license": "MIT"}