/**
 * 响应式缩放 Hook
 * 处理大屏在不同分辨率下的自适应缩放
 */
import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 设计稿基准尺寸
 */
const DESIGN_WIDTH = 1920
const DESIGN_HEIGHT = 1080

/**
 * 响应式缩放 Hook
 * @returns 缩放比例和相关方法
 */
export function useResize() {
  // 当前缩放比例
  const scale = ref(1)

  /**
   * 计算缩放比例
   * 基于窗口尺寸和设计稿尺寸计算最适合的缩放比例
   */
  const calculateScale = () => {
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight

    // 计算宽度和高度的缩放比例
    const scaleX = windowWidth / DESIGN_WIDTH
    const scaleY = windowHeight / DESIGN_HEIGHT

    // 取较小的缩放比例，确保内容完全显示
    scale.value = Math.min(scaleX, scaleY)

    // 最小缩放比例限制，避免内容过小
    if (scale.value < 0.5) {
      scale.value = 0.5
    }

    // 最大缩放比例限制，避免内容过大
    if (scale.value > 2) {
      scale.value = 2
    }
  }

  /**
   * 窗口大小变化处理函数
   * 使用防抖优化性能
   */
  let resizeTimer: number | null = null
  const handleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
    resizeTimer = window.setTimeout(() => {
      calculateScale()
    }, 100)
  }

  /**
   * 组件挂载时的初始化
   */
  onMounted(() => {
    calculateScale()
    window.addEventListener('resize', handleResize)
  })

  /**
   * 组件卸载时的清理
   */
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
  })

  return {
    scale,
    calculateScale
  }
}
