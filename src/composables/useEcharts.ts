/**
 * ECharts 图表 Hook
 * 提供图表初始化、配置和响应式更新功能
 */
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'

/**
 * ECharts Hook 配置选项
 */
interface UseEchartsOptions {
  theme?: string
  renderer?: 'canvas' | 'svg'
  devicePixelRatio?: number
}

/**
 * ECharts Hook 返回值
 */
interface UseEchartsReturn {
  chartInstance: Ref<ECharts | null>
  initChart: (option: EChartsOption) => void
  updateChart: (option: EChartsOption) => void
  resizeChart: () => void
  disposeChart: () => void
  showLoading: (text?: string) => void
  hideLoading: () => void
}

/**
 * ECharts 通用配置
 */
const getDefaultOption = (): Partial<EChartsOption> => ({
  backgroundColor: 'transparent',
  textStyle: {
    color: '#FFFFFF',
    fontFamily: 'Alibaba PuHuiTi, PingFang SC, Microsoft YaHei, sans-serif'
  },
  animation: true,
  animationDuration: 1000,
  animationEasing: 'cubicOut',
  animationDelay: (idx: number) => idx * 200
})

/**
 * ECharts Hook
 * @param container 图表容器元素
 * @param options Hook 配置选项
 */
export function useEcharts(
  container: HTMLElement,
  options: UseEchartsOptions = {}
): UseEchartsReturn {
  const chartInstance = ref<ECharts | null>(null)
  
  const {
    theme = 'dark',
    renderer = 'canvas',
    devicePixelRatio = window.devicePixelRatio || 1
  } = options

  /**
   * 初始化图表
   * @param option ECharts 配置选项
   */
  const initChart = (option: EChartsOption) => {
    if (!container) {
      console.error('图表容器不存在')
      return
    }

    // 销毁已存在的图表实例
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }

    // 创建新的图表实例
    chartInstance.value = echarts.init(container, theme, {
      renderer,
      devicePixelRatio
    })

    // 合并默认配置和用户配置
    const mergedOption = {
      ...getDefaultOption(),
      ...option
    }

    // 设置图表配置
    chartInstance.value.setOption(mergedOption, true)

    // 添加窗口大小变化监听
    window.addEventListener('resize', resizeChart)
  }

  /**
   * 更新图表配置
   * @param option ECharts 配置选项
   * @param notMerge 是否不合并配置
   */
  const updateChart = (option: EChartsOption, notMerge = false) => {
    if (!chartInstance.value) {
      console.warn('图表实例不存在，请先初始化图表')
      return
    }

    chartInstance.value.setOption(option, notMerge)
  }

  /**
   * 调整图表大小
   */
  const resizeChart = () => {
    if (chartInstance.value) {
      nextTick(() => {
        chartInstance.value?.resize()
      })
    }
  }

  /**
   * 销毁图表实例
   */
  const disposeChart = () => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
    window.removeEventListener('resize', resizeChart)
  }

  /**
   * 显示加载动画
   * @param text 加载文本
   */
  const showLoading = (text = '加载中...') => {
    if (chartInstance.value) {
      chartInstance.value.showLoading('default', {
        text,
        color: '#35A8FA',
        textColor: '#FFFFFF',
        maskColor: 'rgba(0, 0, 0, 0.3)',
        zlevel: 0,
        fontSize: 14,
        fontWeight: 'normal',
        fontStyle: 'normal',
        fontFamily: 'Alibaba PuHuiTi, sans-serif'
      })
    }
  }

  /**
   * 隐藏加载动画
   */
  const hideLoading = () => {
    if (chartInstance.value) {
      chartInstance.value.hideLoading()
    }
  }

  /**
   * 组件卸载时清理资源
   */
  onUnmounted(() => {
    disposeChart()
  })

  return {
    chartInstance,
    initChart,
    updateChart,
    resizeChart,
    disposeChart,
    showLoading,
    hideLoading
  }
}

/**
 * 获取图表通用颜色配置
 */
export const getChartColors = () => ({
  primary: '#35A8FA',
  secondary: '#0ACAFF',
  success: '#35A8FA',
  warning: '#FB943C',
  error: '#FF614F',
  info: '#FFC332',
  gradient: {
    primary: {
      type: 'linear' as const,
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: 'rgba(53, 168, 250, 0.8)' },
        { offset: 1, color: 'rgba(53, 168, 250, 0.1)' }
      ]
    },
    secondary: {
      type: 'linear' as const,
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: 'rgba(10, 202, 255, 0.8)' },
        { offset: 1, color: 'rgba(10, 202, 255, 0.1)' }
      ]
    }
  }
})

/**
 * 获取图表通用网格配置
 */
export const getChartGrid = () => ({
  top: 20,
  left: 20,
  right: 20,
  bottom: 20,
  containLabel: true
})

/**
 * 获取图表通用坐标轴配置
 */
export const getChartAxis = () => ({
  xAxis: {
    type: 'category' as const,
    axisLine: {
      lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 0.6)',
      fontSize: 12
    },
    axisTick: {
      show: false
    }
  },
  yAxis: {
    type: 'value' as const,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      color: 'rgba(255, 255, 255, 0.6)',
      fontSize: 12
    },
    splitLine: {
      lineStyle: { color: 'rgba(255, 255, 255, 0.1)' }
    }
  }
})

/**
 * 获取图表通用图例配置
 */
export const getChartLegend = () => ({
  textStyle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12
  },
  itemWidth: 14,
  itemHeight: 14,
  itemGap: 20
})

/**
 * 获取图表通用提示框配置
 */
export const getChartTooltip = () => ({
  trigger: 'axis' as const,
  backgroundColor: 'rgba(0, 0, 0, 0.8)',
  borderColor: 'rgba(53, 168, 250, 0.5)',
  borderWidth: 1,
  textStyle: {
    color: '#FFFFFF',
    fontSize: 12
  },
  extraCssText: 'border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);'
})

export default useEcharts
