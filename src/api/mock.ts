/**
 * Mock 数据定义
 * 提供开发和演示用的模拟数据
 */
import type { StatsData, ChartData, MapData, RealTimeData } from './index'

/**
 * 生成随机数
 */
const random = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成时间序列数据
 */
const generateTimeSeriesData = (count: number, min: number, max: number): number[] => {
  return Array.from({ length: count }, () => random(min, max))
}

/**
 * 生成日期标签
 */
const generateDateLabels = (count: number): string[] => {
  const labels: string[] = []
  const now = new Date()
  
  for (let i = count - 1; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
    labels.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
  }
  
  return labels
}

/**
 * Mock 数据集合
 */
export const mockData = {
  /**
   * 统计数据
   */
  statsData: {
    totalVisits: 1234567,
    onlineUsers: 8456,
    systemLoad: 67.8,
    responseTime: 125,
    trends: {
      visits: generateTimeSeriesData(24, 800, 1500),
      users: generateTimeSeriesData(24, 500, 1200),
      load: generateTimeSeriesData(24, 40, 90),
      response: generateTimeSeriesData(24, 80, 200)
    }
  } as StatsData,

  /**
   * 图表数据
   */
  chartData: {
    // 默认图表数据
    default: {
      categories: generateDateLabels(7),
      series: [
        {
          name: '访问量',
          data: generateTimeSeriesData(7, 1000, 5000),
          type: 'line'
        },
        {
          name: '用户数',
          data: generateTimeSeriesData(7, 500, 2000),
          type: 'line'
        }
      ]
    } as ChartData,

    // 柱状图数据
    bar: {
      categories: ['北京', '上海', '广州', '深圳', '杭州', '成都'],
      series: [
        {
          name: '销售额',
          data: [2340, 1890, 1654, 1423, 1200, 980],
          type: 'bar'
        }
      ]
    } as ChartData,

    // 饼图数据
    pie: {
      categories: ['移动端', '桌面端', '平板端', '其他'],
      series: [
        {
          name: '访问来源',
          data: [65, 25, 8, 2],
          type: 'pie'
        }
      ]
    } as ChartData,

    // 雷达图数据
    radar: {
      categories: ['性能', '安全', '可用性', '扩展性', '维护性'],
      series: [
        {
          name: '系统评分',
          data: [85, 92, 78, 88, 90],
          type: 'radar'
        }
      ]
    } as ChartData,

    // 散点图数据
    scatter: {
      categories: [],
      series: [
        {
          name: '数据分布',
          data: Array.from({ length: 50 }, () => [random(0, 100), random(0, 100)]),
          type: 'scatter'
        }
      ]
    } as ChartData,

    // 热力图数据
    heatmap: {
      categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      series: [
        {
          name: '活跃度',
          data: Array.from({ length: 7 }, (_, i) => 
            Array.from({ length: 24 }, (_, j) => [i, j, random(0, 100)])
          ).flat(),
          type: 'heatmap'
        }
      ]
    } as ChartData
  },

  /**
   * 地图数据
   */
  mapData: [
    { name: '北京', value: 2340, coordinates: [116.4074, 39.9042] },
    { name: '上海', value: 1890, coordinates: [121.4737, 31.2304] },
    { name: '广州', value: 1654, coordinates: [113.2644, 23.1291] },
    { name: '深圳', value: 1423, coordinates: [114.0579, 22.5431] },
    { name: '杭州', value: 1200, coordinates: [120.1551, 30.2741] },
    { name: '成都', value: 980, coordinates: [104.0665, 30.5723] },
    { name: '武汉', value: 856, coordinates: [114.3054, 30.5931] },
    { name: '西安', value: 743, coordinates: [108.9402, 34.3416] },
    { name: '南京', value: 689, coordinates: [118.7969, 32.0603] },
    { name: '重庆', value: 634, coordinates: [106.5516, 29.5630] }
  ] as MapData[],

  /**
   * 实时数据
   */
  realTimeData: Array.from({ length: 20 }, (_, i) => ({
    timestamp: Date.now() - (19 - i) * 60000, // 每分钟一个数据点
    value: random(50, 100),
    status: ['normal', 'warning', 'error'][random(0, 2)] as 'normal' | 'warning' | 'error'
  })) as RealTimeData[],

  /**
   * 系统状态
   */
  systemStatus: {
    status: 'running',
    uptime: 86400 * 15, // 15天
    version: '1.0.0'
  },

  /**
   * 告警数据
   */
  alertData: [
    {
      id: '1',
      level: 'error',
      title: '服务器CPU使用率过高',
      message: '服务器 192.168.1.100 CPU使用率达到95%',
      timestamp: Date.now() - 300000, // 5分钟前
      status: 'active'
    },
    {
      id: '2',
      level: 'warning',
      title: '数据库连接数异常',
      message: '数据库连接数超过阈值80%',
      timestamp: Date.now() - 600000, // 10分钟前
      status: 'active'
    },
    {
      id: '3',
      level: 'info',
      title: '系统更新完成',
      message: '系统已成功更新到版本 1.0.1',
      timestamp: Date.now() - 1800000, // 30分钟前
      status: 'resolved'
    }
  ],

  /**
   * 性能指标数据
   */
  performanceData: {
    cpu: {
      current: random(60, 90),
      history: generateTimeSeriesData(60, 50, 95)
    },
    memory: {
      current: random(70, 85),
      history: generateTimeSeriesData(60, 65, 90)
    },
    disk: {
      current: random(45, 75),
      history: generateTimeSeriesData(60, 40, 80)
    },
    network: {
      inbound: generateTimeSeriesData(60, 100, 500),
      outbound: generateTimeSeriesData(60, 80, 400)
    }
  },

  /**
   * 用户行为数据
   */
  userBehaviorData: {
    pageViews: generateTimeSeriesData(24, 1000, 5000),
    uniqueVisitors: generateTimeSeriesData(24, 500, 2000),
    bounceRate: generateTimeSeriesData(24, 20, 60),
    avgSessionDuration: generateTimeSeriesData(24, 120, 300)
  },

  /**
   * 业务数据
   */
  businessData: {
    revenue: {
      today: random(50000, 100000),
      yesterday: random(45000, 95000),
      thisMonth: random(1000000, 2000000),
      lastMonth: random(900000, 1800000)
    },
    orders: {
      total: random(1000, 5000),
      pending: random(50, 200),
      completed: random(800, 4500),
      cancelled: random(20, 100)
    },
    conversion: {
      rate: random(15, 35) / 10, // 1.5% - 3.5%
      trend: generateTimeSeriesData(30, 10, 40)
    }
  }
}

/**
 * 动态生成实时数据
 * 用于模拟数据的实时更新
 */
export const generateRealTimeUpdate = () => {
  return {
    timestamp: Date.now(),
    value: random(50, 100),
    status: ['normal', 'warning', 'error'][random(0, 2)] as 'normal' | 'warning' | 'error'
  }
}

/**
 * 获取随机颜色
 */
export const getRandomColor = (): string => {
  const colors = [
    '#35A8FA', '#0ACAFF', '#FF614F', '#FB943C', 
    '#FFC332', '#00D4AA', '#9C27B0', '#FF5722'
  ]
  return colors[random(0, colors.length - 1)]
}

export default mockData
