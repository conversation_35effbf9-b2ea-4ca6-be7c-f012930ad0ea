/**
 * API 接口统一管理
 * 支持真实接口和 Mock 数据切换
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { mockData } from './mock'

/**
 * 通用 API 响应接口
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

/**
 * 统计数据接口
 */
export interface StatsData {
  totalVisits: number
  onlineUsers: number
  systemLoad: number
  responseTime: number
  trends: {
    visits: number[]
    users: number[]
    load: number[]
    response: number[]
  }
}

/**
 * 图表数据接口
 */
export interface ChartData {
  categories: string[]
  series: {
    name: string
    data: number[]
    type?: string
  }[]
}

/**
 * 地图数据接口
 */
export interface MapData {
  name: string
  value: number
  coordinates: [number, number]
}

/**
 * 实时数据接口
 */
export interface RealTimeData {
  timestamp: number
  value: number
  status: 'normal' | 'warning' | 'error'
}

/**
 * 创建 Axios 实例
 */
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config: AxiosRequestConfig) => {
      // 添加认证 token
      const token = localStorage.getItem('access_token')
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`
      }

      // 添加时间戳防止缓存
      if (config.params) {
        config.params._t = Date.now()
      } else {
        config.params = { _t: Date.now() }
      }

      console.log('API 请求:', config.method?.toUpperCase(), config.url)
      return config
    },
    (error) => {
      console.error('请求拦截器错误:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      const { data } = response

      // 检查业务状态码
      if (data.code !== 200) {
        console.error('API 业务错误:', data.message)
        throw new Error(data.message || '请求失败')
      }

      console.log('API 响应:', response.config.url, data)
      return response
    },
    (error) => {
      console.error('响应拦截器错误:', error)

      // 处理网络错误
      if (!error.response) {
        console.error('网络错误，请检查网络连接')
        return Promise.reject(new Error('网络错误，请检查网络连接'))
      }

      // 处理 HTTP 状态码错误
      const { status } = error.response
      switch (status) {
        case 401:
          console.error('未授权，请重新登录')
          // 清除本地存储的 token
          localStorage.removeItem('access_token')
          break
        case 403:
          console.error('权限不足')
          break
        case 404:
          console.error('请求的资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`请求失败，状态码: ${status}`)
      }

      return Promise.reject(error)
    }
  )

  return instance
}

// 创建 API 实例
const api = createApiInstance()

/**
 * 检查是否启用 Mock 模式
 */
const isMockEnabled = (): boolean => {
  return import.meta.env.VITE_MOCK_ENABLED === 'true'
}

/**
 * Mock 数据包装器
 * 模拟真实 API 的响应格式和延迟
 */
const mockWrapper = async <T>(data: T, delay = 500): Promise<ApiResponse<T>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, delay))

  return {
    code: 200,
    message: 'success',
    data,
    timestamp: Date.now()
  }
}

/**
 * API 接口定义
 */
export const apiService = {
  /**
   * 获取统计数据
   */
  async getStatsData(): Promise<StatsData> {
    if (isMockEnabled()) {
      const response = await mockWrapper(mockData.statsData)
      return response.data
    }

    const response = await api.get<ApiResponse<StatsData>>('/stats')
    return response.data.data
  },

  /**
   * 获取图表数据
   */
  async getChartData(type: string): Promise<ChartData> {
    if (isMockEnabled()) {
      const response = await mockWrapper(mockData.chartData[type] || mockData.chartData.default)
      return response.data
    }

    const response = await api.get<ApiResponse<ChartData>>(`/chart/${type}`)
    return response.data.data
  },

  /**
   * 获取地图数据
   */
  async getMapData(): Promise<MapData[]> {
    if (isMockEnabled()) {
      const response = await mockWrapper(mockData.mapData)
      return response.data
    }

    const response = await api.get<ApiResponse<MapData[]>>('/map')
    return response.data.data
  },

  /**
   * 获取实时数据
   */
  async getRealTimeData(): Promise<RealTimeData[]> {
    if (isMockEnabled()) {
      const response = await mockWrapper(mockData.realTimeData)
      return response.data
    }

    const response = await api.get<ApiResponse<RealTimeData[]>>('/realtime')
    return response.data.data
  },

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<{ status: string; uptime: number; version: string }> {
    if (isMockEnabled()) {
      const response = await mockWrapper(mockData.systemStatus)
      return response.data
    }

    const response = await api.get<ApiResponse<any>>('/system/status')
    return response.data.data
  }
}

// 导出 API 实例供其他地方使用
export { api }
export default apiService
