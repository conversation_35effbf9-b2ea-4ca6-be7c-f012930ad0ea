/**
 * 全局 SCSS 变量定义
 * 包含颜色、字体、间距等设计系统变量
 */

// ==================== 颜色系统 ====================
// 主背景色（径向渐变）
$bg-primary-start: #0A0E1A;
$bg-primary-end: #060A14;

// 主色调
$color-primary: #35A8FA;
$color-primary-light: #0ACAFF;
$color-primary-dark: #2B86C8;

// 渐变色
$gradient-primary: linear-gradient(135deg, rgba(53, 168, 250, 0.2) 0%, rgba(53, 168, 250, 1) 100%);
$gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);

// 状态色
$color-danger: #FF614F;    // 重大风险
$color-warning: #FB943C;   // 较大风险
$color-info: #FFC332;      // 一般风险
$color-success: #35A8FA;   // 较小风险

// 文本颜色
$text-primary: #FFFFFF;
$text-secondary: rgba(255, 255, 255, 0.85);
$text-tertiary: rgba(255, 255, 255, 0.65);
$text-disabled: rgba(255, 255, 255, 0.45);

// 边框颜色
$border-primary: rgba(53, 168, 250, 0.3);
$border-secondary: rgba(255, 255, 255, 0.1);
$border-tertiary: rgba(255, 255, 255, 0.05);

// 背景颜色
$bg-card: rgba(255, 255, 255, 0.03);
$bg-card-hover: rgba(255, 255, 255, 0.05);
$bg-overlay: rgba(0, 0, 0, 0.8);

// ==================== 字体系统 ====================
// 字体族
$font-family-zh: 'Alibaba PuHuiTi', 'PingFang SC', 'Microsoft YaHei', sans-serif;
$font-family-en: 'DIN Condensed Bold', 'Arial Black', sans-serif;
$font-family-mono: 'Fira Code', 'Consolas', monospace;

// 字体大小（基于 8pt 栅格）
$font-size-xs: 12px;    // 1.5 * 8
$font-size-sm: 14px;    // 1.75 * 8
$font-size-base: 16px;  // 2 * 8
$font-size-lg: 18px;    // 2.25 * 8
$font-size-xl: 20px;    // 2.5 * 8
$font-size-2xl: 24px;   // 3 * 8
$font-size-3xl: 32px;   // 4 * 8
$font-size-4xl: 40px;   // 5 * 8
$font-size-5xl: 48px;   // 6 * 8

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// ==================== 间距系统（8pt 栅格）====================
$spacing-0: 0;
$spacing-1: 8px;    // 1 * 8
$spacing-2: 16px;   // 2 * 8
$spacing-3: 24px;   // 3 * 8
$spacing-4: 32px;   // 4 * 8
$spacing-5: 40px;   // 5 * 8
$spacing-6: 48px;   // 6 * 8
$spacing-8: 64px;   // 8 * 8
$spacing-10: 80px;  // 10 * 8
$spacing-12: 96px;  // 12 * 8

// ==================== 布局尺寸 ====================
// 屏幕尺寸
$screen-width: 1920px;
$screen-height: 1080px;

// 布局区域尺寸
$header-height: 80px;
$footer-height: 40px;
$sidebar-width: 360px;
$main-width: 1200px;
$main-height: 600px;

// ==================== 圆角 ====================
$border-radius-sm: 4px;
$border-radius-base: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-full: 50%;

// ==================== 阴影 ====================
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow-base: 0 4px 8px rgba(0, 0, 0, 0.15);
$shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
$shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
$shadow-glow: 0 0 20px rgba(53, 168, 250, 0.3);

// ==================== 动画 ====================
$transition-fast: 0.15s ease-in-out;
$transition-base: 0.3s ease-in-out;
$transition-slow: 0.8s ease-in-out;

// 动画缓动函数
$ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
$ease-in-back: cubic-bezier(0.6, -0.28, 0.735, 0.045);

// ==================== Z-index 层级 ====================
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
