/**
 * 全局样式文件
 * 包含样式重置、字体引入、通用类等
 */

// 引入字体（CDN）
@import url('https://fonts.googleapis.com/css2?family=DIN+Condensed:wght@400;700&display=swap');
@import url('https://at.alicdn.com/t/c/font_3114978_qe0b39no76.css'); // 阿里巴巴普惠体

// 引入变量
@import './variables.scss';

// ==================== 样式重置 ====================
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: $font-family-zh;
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  color: $text-primary;
  background: radial-gradient(ellipse at center, $bg-primary-start 0%, $bg-primary-end 100%);
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

// ==================== 滚动条样式 ====================
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(53, 168, 250, 0.3);
  border-radius: 3px;
  
  &:hover {
    background: rgba(53, 168, 250, 0.5);
  }
}

// ==================== 通用类 ====================
// 文本类
.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-tertiary { color: $text-tertiary; }
.text-disabled { color: $text-disabled; }

.text-danger { color: $color-danger; }
.text-warning { color: $color-warning; }
.text-info { color: $color-info; }
.text-success { color: $color-success; }

// 字体大小类
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-2xl { font-size: $font-size-2xl; }
.text-3xl { font-size: $font-size-3xl; }
.text-4xl { font-size: $font-size-4xl; }
.text-5xl { font-size: $font-size-5xl; }

// 字重类
.font-light { font-weight: $font-weight-light; }
.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-semibold { font-weight: $font-weight-semibold; }
.font-bold { font-weight: $font-weight-bold; }

// 字体族类
.font-zh { font-family: $font-family-zh; }
.font-en { font-family: $font-family-en; }
.font-mono { font-family: $font-family-mono; }

// 布局类
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.w-screen { width: 100vw; }
.h-screen { height: 100vh; }

// 间距类（基于 8pt 栅格）
.m-0 { margin: $spacing-0; }
.m-1 { margin: $spacing-1; }
.m-2 { margin: $spacing-2; }
.m-3 { margin: $spacing-3; }
.m-4 { margin: $spacing-4; }
.m-5 { margin: $spacing-5; }
.m-6 { margin: $spacing-6; }

.p-0 { padding: $spacing-0; }
.p-1 { padding: $spacing-1; }
.p-2 { padding: $spacing-2; }
.p-3 { padding: $spacing-3; }
.p-4 { padding: $spacing-4; }
.p-5 { padding: $spacing-5; }
.p-6 { padding: $spacing-6; }

// 边框类
.border { border: 1px solid $border-secondary; }
.border-primary { border-color: $border-primary; }
.border-secondary { border-color: $border-secondary; }

.rounded { border-radius: $border-radius-base; }
.rounded-sm { border-radius: $border-radius-sm; }
.rounded-lg { border-radius: $border-radius-lg; }
.rounded-xl { border-radius: $border-radius-xl; }
.rounded-full { border-radius: $border-radius-full; }

// 阴影类
.shadow { box-shadow: $shadow-base; }
.shadow-sm { box-shadow: $shadow-sm; }
.shadow-lg { box-shadow: $shadow-lg; }
.shadow-xl { box-shadow: $shadow-xl; }
.shadow-glow { box-shadow: $shadow-glow; }

// 过渡动画类
.transition { transition: all $transition-base; }
.transition-fast { transition: all $transition-fast; }
.transition-slow { transition: all $transition-slow; }

// ==================== 动画定义 ====================
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(53, 168, 250, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(53, 168, 250, 0.6);
  }
}

// 动画类
.animate-fade-in {
  animation: fadeIn $transition-slow $ease-out-expo;
}

.animate-slide-in-left {
  animation: slideInLeft $transition-slow $ease-out-expo;
}

.animate-slide-in-right {
  animation: slideInRight $transition-slow $ease-out-expo;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shake {
  animation: shake $transition-base;
}

.animate-glow {
  animation: glow 2s infinite;
}
