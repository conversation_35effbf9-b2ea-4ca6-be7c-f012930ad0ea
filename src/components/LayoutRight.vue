<!--
  右侧布局组件
  显示实时监控和告警信息
-->
<template>
  <aside class="layout-right">
    <!-- 实时监控 -->
    <div class="monitor-section">
      <h3 class="section-title">实时监控</h3>
      <div class="monitor-grid">
        <div 
          v-for="(monitor, index) in monitorData" 
          :key="monitor.id"
          class="monitor-card"
          :class="monitor.statusClass"
          :style="{ animationDelay: `${index * 0.15}s` }"
        >
          <div class="monitor-header">
            <span class="monitor-name">{{ monitor.name }}</span>
            <span class="monitor-status" :class="monitor.statusClass">
              {{ monitor.status }}
            </span>
          </div>
          <div class="monitor-value font-en">{{ monitor.value }}</div>
          <div class="monitor-chart">
            <div 
              v-for="(point, i) in monitor.chartData" 
              :key="i"
              class="chart-bar"
              :style="{ 
                height: `${point}%`,
                animationDelay: `${(index * 0.15) + (i * 0.05)}s`
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警信息 -->
    <div class="alert-section">
      <h3 class="section-title">
        告警信息
        <span class="alert-count">{{ activeAlerts }}</span>
      </h3>
      <div class="alert-list">
        <div 
          v-for="(alert, index) in alertList" 
          :key="alert.id"
          class="alert-item"
          :class="alert.level"
          :style="{ animationDelay: `${(index + 4) * 0.15}s` }"
        >
          <div class="alert-indicator">
            <div class="alert-dot"></div>
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
          </div>
          <div class="alert-action">
            <button class="action-btn" @click="handleAlert(alert.id)">
              处理
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能指标 -->
    <div class="performance-section">
      <h3 class="section-title">性能指标</h3>
      <div class="performance-chart">
        <div ref="performanceChartRef" class="chart-wrapper"></div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useEcharts } from '@/composables/useEcharts'

  /**
   * 监控数据接口定义
   */
  interface MonitorData {
    id: string
    name: string
    value: string
    status: string
    statusClass: string
    chartData: number[]
  }

  /**
   * 告警数据接口定义
   */
  interface AlertData {
    id: string
    level: 'error' | 'warning' | 'info'
    title: string
    message: string
    timestamp: number
    status: 'active' | 'resolved'
  }

  // 图表容器引用
  const performanceChartRef = ref<HTMLElement>()

  // 监控数据
  const monitorData = ref<MonitorData[]>([
    {
      id: '1',
      name: 'CPU 使用率',
      value: '68.5%',
      status: '正常',
      statusClass: 'normal',
      chartData: [45, 52, 48, 61, 68, 55, 72, 65, 58, 63]
    },
    {
      id: '2',
      name: '内存使用率',
      value: '72.3%',
      status: '正常',
      statusClass: 'normal',
      chartData: [65, 68, 72, 69, 75, 71, 68, 72, 70, 73]
    },
    {
      id: '3',
      name: '磁盘使用率',
      value: '45.8%',
      status: '正常',
      statusClass: 'normal',
      chartData: [42, 45, 43, 48, 46, 44, 47, 45, 43, 46]
    },
    {
      id: '4',
      name: '网络延迟',
      value: '12ms',
      status: '优秀',
      statusClass: 'excellent',
      chartData: [15, 12, 14, 11, 13, 12, 10, 12, 11, 12]
    }
  ])

  // 告警数据
  const alertList = ref<AlertData[]>([
    {
      id: '1',
      level: 'error',
      title: '服务器响应异常',
      message: '服务器 192.168.1.100 响应时间超过阈值',
      timestamp: Date.now() - 300000,
      status: 'active'
    },
    {
      id: '2',
      level: 'warning',
      title: '数据库连接数过高',
      message: '数据库连接数达到80%，建议优化',
      timestamp: Date.now() - 600000,
      status: 'active'
    },
    {
      id: '3',
      level: 'info',
      title: '系统更新完成',
      message: '系统已成功更新到最新版本',
      timestamp: Date.now() - 1800000,
      status: 'resolved'
    },
    {
      id: '4',
      level: 'warning',
      title: '存储空间不足',
      message: '磁盘使用率超过85%，请及时清理',
      timestamp: Date.now() - 3600000,
      status: 'active'
    }
  ])

  /**
   * 计算活跃告警数量
   */
  const activeAlerts = computed(() => {
    return alertList.value.filter(alert => alert.status === 'active').length
  })

  /**
   * 格式化时间显示
   */
  const formatTime = (timestamp: number): string => {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (days > 0) return `${days}天前`
    if (hours > 0) return `${hours}小时前`
    if (minutes > 0) return `${minutes}分钟前`
    return '刚刚'
  }

  /**
   * 处理告警
   */
  const handleAlert = (alertId: string) => {
    const alert = alertList.value.find(item => item.id === alertId)
    if (alert) {
      alert.status = 'resolved'
      console.log(`处理告警: ${alert.title}`)
    }
  }

  /**
   * 初始化性能图表
   */
  const initPerformanceChart = () => {
    if (!performanceChartRef.value) return

    const { initChart } = useEcharts(performanceChartRef.value)

    const option = {
      grid: {
        top: 20,
        left: 20,
        right: 20,
        bottom: 20,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['CPU', '内存', '磁盘', '网络'],
        axisLine: {
          lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: { color: 'rgba(255, 255, 255, 0.1)' }
        }
      },
      series: [
        {
          data: [68.5, 72.3, 45.8, 12],
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: '#35A8FA' },
                { offset: 1, color: 'rgba(53, 168, 250, 0.3)' }
              ]
            },
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              color: '#0ACAFF'
            }
          }
        }
      ]
    }

    initChart(option)
  }

  /**
   * 组件挂载时初始化图表
   */
  onMounted(() => {
    setTimeout(() => {
      initPerformanceChart()
    }, 1200)
  })
</script>

<style lang="scss" scoped>
  .layout-right {
    width: 360px;
    height: 600px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    animation: slideInRight 0.8s ease-out;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
    margin-bottom: 16px;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #35A8FA 0%, #0ACAFF 100%);
      border-radius: 2px;
    }
  }

  .alert-count {
    background: #FF614F;
    color: #FFFFFF;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 700;
  }

  .monitor-section {
    flex-shrink: 0;
  }

  .monitor-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .monitor-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease-out both;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      transform: translateY(-2px);
    }

    &.normal {
      border-left: 3px solid #35A8FA;
    }

    &.excellent {
      border-left: 3px solid #35A8FA;
    }

    &.warning {
      border-left: 3px solid #FB943C;
    }

    &.error {
      border-left: 3px solid #FF614F;
    }
  }

  .monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .monitor-name {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
  }

  .monitor-status {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;

    &.normal {
      background: rgba(53, 168, 250, 0.2);
      color: #35A8FA;
    }

    &.excellent {
      background: rgba(53, 168, 250, 0.2);
      color: #35A8FA;
    }

    &.warning {
      background: rgba(251, 148, 60, 0.2);
      color: #FB943C;
    }

    &.error {
      background: rgba(255, 97, 79, 0.2);
      color: #FF614F;
    }
  }

  .monitor-value {
    font-size: 18px;
    font-weight: 700;
    color: #FFFFFF;
    margin-bottom: 8px;
  }

  .monitor-chart {
    display: flex;
    align-items: end;
    gap: 2px;
    height: 20px;
  }

  .chart-bar {
    flex: 1;
    background: linear-gradient(to top, #35A8FA, rgba(53, 168, 250, 0.3));
    border-radius: 1px;
    min-height: 2px;
    animation: chartBarGrow 0.8s ease-out both;
  }

  .alert-section {
    flex: 1;
    min-height: 200px;
  }

  .alert-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
  }

  .alert-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease-out both;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    &.error {
      border-left: 3px solid #FF614F;
      
      .alert-dot {
        background: #FF614F;
        box-shadow: 0 0 8px rgba(255, 97, 79, 0.4);
      }
    }

    &.warning {
      border-left: 3px solid #FB943C;
      
      .alert-dot {
        background: #FB943C;
        box-shadow: 0 0 8px rgba(251, 148, 60, 0.4);
      }
    }

    &.info {
      border-left: 3px solid #35A8FA;
      
      .alert-dot {
        background: #35A8FA;
        box-shadow: 0 0 8px rgba(53, 168, 250, 0.4);
      }
    }
  }

  .alert-indicator {
    flex-shrink: 0;
    padding-top: 2px;
  }

  .alert-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  .alert-content {
    flex: 1;
  }

  .alert-title {
    font-size: 14px;
    color: #FFFFFF;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .alert-message {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
    margin-bottom: 4px;
  }

  .alert-time {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
  }

  .alert-action {
    flex-shrink: 0;
  }

  .action-btn {
    background: rgba(53, 168, 250, 0.2);
    border: 1px solid rgba(53, 168, 250, 0.3);
    color: #35A8FA;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(53, 168, 250, 0.3);
      border-color: rgba(53, 168, 250, 0.5);
    }
  }

  .performance-section {
    flex-shrink: 0;
  }

  .performance-chart {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    height: 120px;
  }

  .chart-wrapper {
    width: 100%;
    height: 100%;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes chartBarGrow {
    from {
      height: 0;
    }
    to {
      height: var(--height);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
</style>
