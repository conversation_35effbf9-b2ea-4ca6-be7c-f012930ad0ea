<!--
  左侧布局组件
  显示数据统计卡片和图表
-->
<template>
  <aside class="layout-left">
    <!-- 数据统计卡片 -->
    <div class="stats-section">
      <h3 class="section-title">数据概览</h3>
      <div class="stats-grid">
        <div 
          v-for="(stat, index) in statsData" 
          :key="stat.id"
          class="stat-card"
          :style="{ animationDelay: `${index * 0.2}s` }"
        >
          <div class="stat-icon" :class="stat.iconClass">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value font-en">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-trend" :class="stat.trendClass">
              <span class="trend-icon">{{ stat.trend > 0 ? '↗' : '↘' }}</span>
              <span class="trend-value">{{ Math.abs(stat.trend) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <h3 class="section-title">趋势分析</h3>
      <div class="chart-container">
        <div ref="trendChartRef" class="chart-wrapper"></div>
      </div>
    </div>

    <!-- 状态列表 -->
    <div class="status-section">
      <h3 class="section-title">系统状态</h3>
      <div class="status-list">
        <div 
          v-for="(status, index) in statusList" 
          :key="status.id"
          class="status-item"
          :class="status.statusClass"
          :style="{ animationDelay: `${(index + 3) * 0.2}s` }"
        >
          <div class="status-indicator"></div>
          <div class="status-info">
            <div class="status-name">{{ status.name }}</div>
            <div class="status-desc">{{ status.description }}</div>
          </div>
          <div class="status-value">{{ status.value }}</div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { useEcharts } from '@/composables/useEcharts'

  /**
   * 统计数据接口定义
   */
  interface StatData {
    id: string
    label: string
    value: string
    trend: number
    icon: string
    iconClass: string
    trendClass: string
  }

  /**
   * 状态项接口定义
   */
  interface StatusItem {
    id: string
    name: string
    description: string
    value: string
    statusClass: string
  }

  // 图表容器引用
  const trendChartRef = ref<HTMLElement>()

  // 统计数据
  const statsData = ref<StatData[]>([
    {
      id: '1',
      label: '总访问量',
      value: '1,234,567',
      trend: 12.5,
      icon: 'icon-visit',
      iconClass: 'primary',
      trendClass: 'up'
    },
    {
      id: '2',
      label: '在线用户',
      value: '8,456',
      trend: -3.2,
      icon: 'icon-user',
      iconClass: 'success',
      trendClass: 'down'
    },
    {
      id: '3',
      label: '系统负载',
      value: '67.8%',
      trend: 5.1,
      icon: 'icon-cpu',
      iconClass: 'warning',
      trendClass: 'up'
    },
    {
      id: '4',
      label: '响应时间',
      value: '125ms',
      trend: -8.7,
      icon: 'icon-time',
      iconClass: 'info',
      trendClass: 'down'
    }
  ])

  // 状态列表
  const statusList = ref<StatusItem[]>([
    {
      id: '1',
      name: '数据库服务',
      description: '运行正常',
      value: '99.9%',
      statusClass: 'normal'
    },
    {
      id: '2',
      name: 'API 服务',
      description: '运行正常',
      value: '99.8%',
      statusClass: 'normal'
    },
    {
      id: '3',
      name: '缓存服务',
      description: '轻微延迟',
      value: '95.2%',
      statusClass: 'warning'
    },
    {
      id: '4',
      name: '消息队列',
      description: '运行正常',
      value: '99.5%',
      statusClass: 'normal'
    }
  ])

  /**
   * 初始化趋势图表
   */
  const initTrendChart = () => {
    if (!trendChartRef.value) return

    const { initChart } = useEcharts(trendChartRef.value)

    const option = {
      grid: {
        top: 20,
        left: 20,
        right: 20,
        bottom: 20,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
        axisLine: {
          lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12
        },
        splitLine: {
          lineStyle: { color: 'rgba(255, 255, 255, 0.1)' }
        }
      },
      series: [
        {
          data: [820, 932, 901, 934, 1290, 1330],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#35A8FA',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(53, 168, 250, 0.3)' },
                { offset: 1, color: 'rgba(53, 168, 250, 0.05)' }
              ]
            }
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#35A8FA',
            borderColor: '#fff',
            borderWidth: 2
          }
        }
      ]
    }

    initChart(option)
  }

  /**
   * 组件挂载时初始化图表
   */
  onMounted(() => {
    setTimeout(() => {
      initTrendChart()
    }, 1000)
  })
</script>

<style lang="scss" scoped>
  .layout-left {
    width: 360px;
    height: 600px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    animation: slideInLeft 0.8s ease-out;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
    margin-bottom: 16px;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #35A8FA 0%, #0ACAFF 100%);
      border-radius: 2px;
    }
  }

  .stats-section {
    flex-shrink: 0;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .stat-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease-out both;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(53, 168, 250, 0.3);
      transform: translateY(-2px);
    }
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;

    &.primary { background: rgba(53, 168, 250, 0.2); color: #35A8FA; }
    &.success { background: rgba(53, 168, 250, 0.2); color: #35A8FA; }
    &.warning { background: rgba(251, 148, 60, 0.2); color: #FB943C; }
    &.info { background: rgba(255, 195, 50, 0.2); color: #FFC332; }
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 16px;
    font-weight: 700;
    color: #FFFFFF;
    line-height: 1;
  }

  .stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 2px;
  }

  .stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
    font-size: 12px;

    &.up { color: #35A8FA; }
    &.down { color: #FF614F; }
  }

  .chart-section {
    flex: 1;
    min-height: 200px;
  }

  .chart-container {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    height: 200px;
  }

  .chart-wrapper {
    width: 100%;
    height: 100%;
  }

  .status-section {
    flex-shrink: 0;
  }

  .status-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .status-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease-out both;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    &.normal .status-indicator {
      background: #35A8FA;
      box-shadow: 0 0 8px rgba(53, 168, 250, 0.4);
    }

    &.warning .status-indicator {
      background: #FB943C;
      box-shadow: 0 0 8px rgba(251, 148, 60, 0.4);
    }

    &.error .status-indicator {
      background: #FF614F;
      box-shadow: 0 0 8px rgba(255, 97, 79, 0.4);
    }
  }

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .status-info {
    flex: 1;
  }

  .status-name {
    font-size: 14px;
    color: #FFFFFF;
    font-weight: 500;
  }

  .status-desc {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 2px;
  }

  .status-value {
    font-size: 14px;
    font-weight: 600;
    color: #35A8FA;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
</style>
