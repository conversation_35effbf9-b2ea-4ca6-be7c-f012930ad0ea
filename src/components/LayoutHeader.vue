<!--
  顶部布局组件
  显示系统标题、时间和状态信息
-->
<template>
  <header class="layout-header">
    <div class="header-content">
      <!-- 左侧标题区域 -->
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon"></div>
          <h1 class="system-title font-en">{{ systemTitle }}</h1>
        </div>
      </div>

      <!-- 中间标题区域 -->
      <div class="header-center">
        <h2 class="main-title font-zh">{{ mainTitle }}</h2>
        <div class="title-decoration"></div>
      </div>

      <!-- 右侧信息区域 -->
      <div class="header-right">
        <div class="time-section">
          <div class="current-time font-en">{{ currentTime }}</div>
          <div class="current-date font-zh">{{ currentDate }}</div>
        </div>
        <div class="status-indicator" :class="{ active: systemStatus }">
          <span class="status-dot"></span>
          <span class="status-text">{{ systemStatus ? '系统正常' : '系统异常' }}</span>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'

  /**
   * 组件属性定义
   */
  interface Props {
    systemTitle?: string
    mainTitle?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    systemTitle: 'BIG SCREEN SYSTEM',
    mainTitle: '大屏可视化数据中心'
  })

  // 响应式数据
  const currentTime = ref('')
  const currentDate = ref('')
  const systemStatus = ref(true)

  /**
   * 更新时间显示
   */
  const updateTime = () => {
    const now = new Date()
    
    // 格式化时间 HH:MM:SS
    currentTime.value = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })

    // 格式化日期 YYYY年MM月DD日
    currentDate.value = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // 定时器
  let timeTimer: number | null = null

  /**
   * 组件挂载时启动定时器
   */
  onMounted(() => {
    updateTime()
    timeTimer = window.setInterval(updateTime, 1000)
  })

  /**
   * 组件卸载时清理定时器
   */
  onUnmounted(() => {
    if (timeTimer) {
      clearInterval(timeTimer)
    }
  })
</script>

<style lang="scss" scoped>
  .layout-header {
    width: 100%;
    height: 80px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border-bottom: 1px solid rgba(53, 168, 250, 0.3);
    position: relative;
    z-index: 100;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, #35A8FA 50%, transparent 100%);
    }
  }

  .header-content {
    height: 100%;
    padding: 0 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header-left {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #35A8FA 0%, #0ACAFF 100%);
    border-radius: 8px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 20px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 4px;
    }
  }

  .system-title {
    font-size: 24px;
    font-weight: 700;
    color: #35A8FA;
    letter-spacing: 2px;
  }

  .header-center {
    flex: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .main-title {
    font-size: 32px;
    font-weight: 600;
    color: #FFFFFF;
    text-align: center;
    letter-spacing: 4px;
  }

  .title-decoration {
    width: 200px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #35A8FA 50%, transparent 100%);
  }

  .header-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 24px;
  }

  .time-section {
    text-align: right;
  }

  .current-time {
    font-size: 24px;
    font-weight: 700;
    color: #0ACAFF;
    line-height: 1;
  }

  .current-date {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 4px;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &.active {
      border-color: rgba(53, 168, 250, 0.5);
      background: rgba(53, 168, 250, 0.1);

      .status-dot {
        background: #35A8FA;
        box-shadow: 0 0 10px rgba(53, 168, 250, 0.6);
      }
    }
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #FF614F;
    transition: all 0.3s ease;
  }

  .status-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
  }

  // 入场动画
  .layout-header {
    animation: slideInTop 0.8s ease-out;
  }

  @keyframes slideInTop {
    from {
      opacity: 0;
      transform: translateY(-80px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
