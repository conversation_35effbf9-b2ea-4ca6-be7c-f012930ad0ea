<!--
  根组件
  定义应用的整体布局结构和响应式缩放
-->
<template>
  <div id="app" class="app-container" :style="scaleStyle">
    <ScreenDashboard />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import ScreenDashboard from '@/views/ScreenDashboard.vue'
  import { useResize } from '@/composables/useResize'

  /**
   * 使用响应式缩放 Hook
   * 确保在不同分辨率下保持 1920x1080 的设计比例
   */
  const { scale } = useResize()

  /**
   * 计算缩放样式
   * 基于当前窗口尺寸动态调整缩放比例
   */
  const scaleStyle = computed(() => ({
    transform: `scale(${scale.value})`,
    transformOrigin: 'top left',
    width: '1920px',
    height: '1080px'
  }))
</script>

<style lang="scss" scoped>
  .app-container {
    position: relative;
    overflow: hidden;
    background: radial-gradient(ellipse at center, #0A0E1A 0%, #060A14 100%);
  }
</style>
